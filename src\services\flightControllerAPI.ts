
const API_BASE_URL = 'http://***********'; // ESP32 AP default IP

export interface FlightData {
  armed: boolean;
  flightMode: string;
  battery: {
    voltage: number;
    percentage: number;
  };
  gps: {
    connected: boolean;
    satellites: number;
    latitude: number;
    longitude: number;
  };
  sensors: {
    gyro: { x: number; y: number; z: number };
    accel: { x: number; y: number; z: number };
    mag: { x: number; y: number; z: number };
    pressure: number;
    altitude: number;
  };
  pid: {
    roll: { p: number; i: number; d: number };
    pitch: { p: number; i: number; d: number };
    yaw: { p: number; i: number; d: number };
  };
}

class FlightControllerAPI {
  private async fetchAPI(endpoint: string, options: RequestInit = {}) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API call failed for ${endpoint}:`, error);
      throw error;
    }
  }

  async getTelemetry(): Promise<FlightData> {
    return this.fetchAPI('/api/telemetry');
  }

  async arm(): Promise<void> {
    await this.fetchAPI('/api/arm', { method: 'POST' });
  }

  async disarm(): Promise<void> {
    await this.fetchAPI('/api/disarm', { method: 'POST' });
  }

  async setFlightMode(mode: string): Promise<void> {
    await this.fetchAPI('/api/flightmode', {
      method: 'POST',
      body: JSON.stringify({ mode }),
    });
  }

  async setPID(pidData: any): Promise<void> {
    await this.fetchAPI('/api/pid', {
      method: 'POST',
      body: JSON.stringify(pidData),
    });
  }

  async calibrateSensor(sensor: string): Promise<void> {
    await this.fetchAPI('/api/calibrate', {
      method: 'POST',
      body: JSON.stringify({ sensor }),
    });
  }

  async getConfig(): Promise<any> {
    return this.fetchAPI('/api/config');
  }

  async setConfig(config: any): Promise<void> {
    await this.fetchAPI('/api/config', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  }
}

export const flightControllerAPI = new FlightControllerAPI();
